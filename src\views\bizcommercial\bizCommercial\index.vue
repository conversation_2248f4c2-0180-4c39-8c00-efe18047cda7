<template>
  <div class="table-box">
    <ProTable
      ref="proTableRef"
      title="广告信息表"
      :indent="20"
      :columns="columns"
      :search-columns="searchColumns"
      :request-api="getTableList"
      row-key="commercialId"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="scope">
        <el-button type="primary"
          v-auth="'biz.commercial.create'"
          :icon="CirclePlus"
          @click="openAddEdit('新增广告信息表')"
        >
          新增
        </el-button>
        <el-button
          v-auth="'biz.commercial.remove'"
          type="danger"
          :icon="Delete"
          plain
          :disabled="!scope.isSelected"
          @click="batchDelete(scope.selectedListIds)"
        >
          批量删除
        </el-button>
        <el-button
          v-auth="'biz.commercial.import'"
          type="primary"
          :icon="Upload"
          plain
          @click="importData"
        >
          导入
        </el-button>
        <el-button
          v-auth="'biz.commercial.export'"
          type="primary"
          :icon="Download"
          plain
          @click="downloadFile"
        >
          导出
        </el-button>
      </template>
      <template #operation="{ row }">
        <el-button
          v-auth="'biz.commercial.update'"
          type="primary"
          link
          :icon="EditPen"
          @click="openAddEdit('编辑广告信息表', row, false)"
        >
          编辑
        </el-button>
        <el-button
            v-auth="'biz.commercial.remove'"
          type="primary"
          link
          :icon="Delete"
          @click="deleteInfo(row)"
        >
          删除
        </el-button>
      </template>
    </ProTable>
    <BizCommercialForm ref="bizCommercialRef" />
    <ImportExcel ref="ImportExcelRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  CirclePlus,
  Delete,
  EditPen,
  Upload,
  Download,
} from '@element-plus/icons-vue'
import ProTable from '@/components/ProTable/index.vue'
import {
  createBizCommercialApi,
  removeBizCommercialApi,
  updateBizCommercialApi,
  getBizCommercialListApi,
  getBizCommercialDetailApi,
  importBizCommercialExcelApi,
  exportBizCommercialExcelApi,
} from '@/api/modules/bizcommercial/bizCommercial';
import { useHandleData } from '@/hooks/useHandleData';
import BizCommercialForm from '@/views/bizcommercial/bizCommercial/components/BizCommercialForm.vue';
import type { ColumnProps, ProTableInstance, SearchProps } from '@/components/ProTable/interface';
import type { BizCommercialQuery, BizCommercialRow } from '@/api/types/bizcommercial/bizCommercial';
import ImportExcel from '@/components/ImportExcel/index.vue';
import { downloadTemplate } from '@/api/modules/system/common';
import { useDownload } from "@/hooks/useDownload";
defineOptions({
  name: 'BizCommercialView'
})
const proTableRef = ref<ProTableInstance>();
// 表格配置项
const columns: ColumnProps<BizCommercialRow>[] = [
  { type: 'selection', width: 80 },
  { prop: 'fileId', label: '文件ID' },
  { prop: 'createTime', label: '创建时间' },
  { prop: 'updateTime', label: '更新时间' },
  { prop: 'sortNum', label: '显示顺序' },
  { prop: 'remark', label: '备注' },
  { prop: 'operation', label: '操作', width: 250, fixed: 'right' }
]
// 搜索条件项
const searchColumns: SearchProps[] = [
  { prop: 'createTime',
    label: '创建时间',
    el: 'date-picker',
    span: 2,
    props: {
      type: "datetimerange",
      valueFormat: "YYYY-MM-DD HH:mm:ss"
    },
  },
]
// 获取table列表
const getTableList = (params: BizCommercialQuery) => {
  let newParams = formatParams(params);
  return getBizCommercialListApi(newParams);
};
const formatParams = (params: BizCommercialQuery) =>{
  let newParams = JSON.parse(JSON.stringify(params));
  if(newParams.updateTime) {
    newParams.updateTimeStart = newParams.updateTime[0];
    newParams.updateTimeEnd = newParams.updateTime[1];
    delete newParams.updateTime;
  }

  return newParams;
}
// 打开 drawer(新增、查看、编辑)
const bizCommercialRef = ref<InstanceType<typeof BizCommercialForm>>()
const openAddEdit = async(title: string, row: any = {}, isAdd = true) => {
  if (!isAdd) {
    const record = await getBizCommercialDetailApi({ id: row?.commercialId })
    row = record?.data
  }
  const params = {
    title,
    row: { ...row },
    api: isAdd ? createBizCommercialApi : updateBizCommercialApi,
    getTableList: proTableRef.value?.getTableList
  }
  bizCommercialRef.value?.acceptParams(params)
}
// 删除信息
const deleteInfo = async (params: BizCommercialRow) => {
  await useHandleData(
    removeBizCommercialApi,
    { ids: [params.commercialId] },
    `删除【${params.commercialId}】广告信息表`
  )
  proTableRef.value?.getTableList()
}
// 批量删除信息
const batchDelete = async (ids: (string | number)[]) => {
  await useHandleData(removeBizCommercialApi, { ids }, '删除所选广告信息表')
  proTableRef.value?.clearSelection()
  proTableRef.value?.getTableList()
}
// 导入
const ImportExcelRef = ref<InstanceType<typeof ImportExcel>>()
const importData = () => {
  const params = {
    title: '广告信息表',
    templateName: '广告信息表',
    tempApi: downloadTemplate,
    importApi: importBizCommercialExcelApi,
    getTableList: proTableRef.value?.getTableList
  }
  ImportExcelRef.value?.acceptParams(params)
}
// 导出
const downloadFile = async () => {
  let newParams = formatParams(proTableRef.value?.searchParam as BizCommercialQuery);
  useDownload(exportBizCommercialExcelApi, "广告信息表", newParams);
};
</script>