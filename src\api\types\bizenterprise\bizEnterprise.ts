import type { IPageQuery } from '@/api/types'

// 查询条件
export type BizEnterpriseQuery = IPageQuery & {
    enterpriseName?: string
    creditCode?: string
    leaderContact?: string
    createTime?: string
  }

// 编辑form表单
export type BizEnterpriseForm = {
    enterpriseName?: string
    creditCode?: string
    leaderContact?: string
    remark?: string
 }

// list或detail返回结构
export type BizEnterpriseRow = {
    enterpriseName?: string
    creditCode?: string
    leaderContact?: string
    createId?: number
    createTime?: string
    updateTime?: string
  }

